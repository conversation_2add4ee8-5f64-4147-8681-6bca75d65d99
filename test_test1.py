# Generated by Selenium IDE
import pytest
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities

class TestTest1():
  def setup_method(self, method):
    self.driver = webdriver.Chrome()
    self.vars = {}
  
  def teardown_method(self, method):
    self.driver.quit()
  
  def test_test1(self):
    # Test name: Test 1
    # Step # | name | target | value
    # 1 | open | /psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_LANDINGPAGE.GBL | 
    self.driver.get("https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_LANDINGPAGE.GBL")
    # 2 | setWindowSize | 1536x816 | 
    self.driver.set_window_size(1536, 816)
    # 3 | click | id=win0groupletPTNUI_LAND_REC_GROUPLET$3 | 
    self.driver.find_element(By.ID, "win0groupletPTNUI_LAND_REC_GROUPLET$3").click()
    # 4 | selectFrame | index=0 | 
    self.driver.switch_to.frame(0)
    # 5 | click | id=STDNT_CAR_SRCH_EMPLID | 
    self.driver.find_element(By.ID, "STDNT_CAR_SRCH_EMPLID").click()
    # 6 | click | id=STDNT_CAR_SRCH_EMPLID | 
    self.driver.find_element(By.ID, "STDNT_CAR_SRCH_EMPLID").click()
    # 7 | click | id=PTS_CFG_CL_WRK_PTS_SRCH_BTN | 
    self.driver.find_element(By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN").click()
    # 8 | click | id=PTS_CFG_CL_RSLT_NUI_SRCH3$14$$1 | 
    self.driver.find_element(By.ID, "PTS_CFG_CL_RSLT_NUI_SRCH3$14$$1").click()
    # 9 | click | id=SSR_ACDPRG_AUS$new$0$$0 | 
    self.driver.find_element(By.ID, "SSR_ACDPRG_AUS$new$0$$0").click()
    # 10 | click | id=ACAD_PROG_PROG_ACTION$0 | 
    self.driver.find_element(By.ID, "ACAD_PROG_PROG_ACTION$0").click()
    # 11 | type | id=ACAD_PROG_PROG_ACTION$0 | Data
    self.driver.find_element(By.ID, "ACAD_PROG_PROG_ACTION$0").send_keys("Data")
    # 12 | type | id=ACAD_PROG_PROG_REASON$0 | PGRD
    self.driver.find_element(By.ID, "ACAD_PROG_PROG_REASON$0").send_keys("PGRD")
    # 13 | click | css=#ICTAB_8 > span | 
    self.driver.find_element(By.CSS_SELECTOR, "#ICTAB_8 > span").click()
    # 14 | click | id=LU_SUP_LU_TBL_LU_SUPERVISOR$prompt$img$0 | 
    self.driver.find_element(By.ID, "LU_SUP_LU_TBL_LU_SUPERVISOR$prompt$img$0").click()
    # 15 | selectFrame | relative=parent | 
    self.driver.switch_to.default_content()
    # 16 | click | id=ptModCloseImg_0 | 
    self.driver.find_element(By.ID, "ptModCloseImg_0").click()
    # 17 | selectFrame | index=0 | 
    self.driver.switch_to.frame(0)
    # 18 | click | id=LU_SUP_LU_TBL_LU_SUPERVISOR$0 | 
    self.driver.find_element(By.ID, "LU_SUP_LU_TBL_LU_SUPERVISOR$0").click()
    # 19 | type | id=LU_SUP_LU_TBL_LU_SUPERVISOR$0 | 8000401
    self.driver.find_element(By.ID, "LU_SUP_LU_TBL_LU_SUPERVISOR$0").send_keys("8000401")
    # 20 | click | id=#ICSave | 
    self.driver.find_element(By.ID, "#ICSave").click()
  
